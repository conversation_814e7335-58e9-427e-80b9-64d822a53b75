{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>"], "description": "文件系统操作服务器，允许读写文件和目录操作"}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "description": "知识图谱记忆服务器，用于存储和检索信息"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "顺序思考服务器，帮助结构化问题解决过程"}, "git": {"command": "./mcp_env/bin/python", "args": ["-m", "mcp_server_git", "--repository", "/home/<USER>/mcp"], "description": "Git版本控制服务器，提供Git操作功能"}, "time": {"command": "./mcp_env/bin/python", "args": ["-m", "mcp_server_time"], "description": "时间服务器，提供日期时间相关功能"}}}