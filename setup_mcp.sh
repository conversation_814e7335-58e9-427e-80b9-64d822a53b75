#!/bin/bash

# MCP服务器安装和配置脚本
# 安装常用的免费MCP服务器，无需API密钥

echo "🚀 开始安装MCP服务器..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装，请先安装Python3"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"
echo "✅ Python版本: $(python3 --version)"

# 创建Python虚拟环境（如果不存在）
if [ ! -d "mcp_env" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv mcp_env
fi

# 激活虚拟环境
source mcp_env/bin/activate

# 升级pip
echo "📦 升级pip..."
pip install --upgrade pip

# 安装MCP核心库
echo "📦 安装MCP核心库..."
pip install mcp

# 安装官方Node.js MCP服务器
echo "📦 安装官方Node.js MCP服务器..."
npm install -g @modelcontextprotocol/server-filesystem
npm install -g @modelcontextprotocol/server-memory
npm install -g @modelcontextprotocol/server-sequential-thinking

# 安装Python MCP服务器
echo "📦 安装Python MCP服务器..."
pip install mcp-server-git
pip install mcp-server-time

echo "✅ 所有MCP服务器安装完成！"

# 创建Claude Desktop配置目录
CLAUDE_CONFIG_DIR="$HOME/.config/claude-desktop"
if [ ! -d "$CLAUDE_CONFIG_DIR" ]; then
    echo "📁 创建Claude Desktop配置目录..."
    mkdir -p "$CLAUDE_CONFIG_DIR"
fi

# 复制配置文件
echo "📋 复制Claude Desktop配置..."
cp claude_desktop_config.json "$CLAUDE_CONFIG_DIR/claude_desktop_config.json"

echo "🎉 MCP服务器安装和配置完成！"
echo ""
echo "📋 已安装的MCP服务器："
echo "  • filesystem - 文件系统操作"
echo "  • memory - 知识图谱记忆"
echo "  • sequential-thinking - 顺序思考"
echo "  • git - Git版本控制"
echo "  • time - 时间日期功能"
echo ""
echo "📝 配置文件位置："
echo "  • Claude Desktop: $CLAUDE_CONFIG_DIR/claude_desktop_config.json"
echo "  • 本地配置: ./claude_desktop_config.json"
echo ""
echo "🔄 请重启Claude Desktop以加载新的MCP服务器配置"
