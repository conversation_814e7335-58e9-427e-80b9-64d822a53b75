#!/usr/bin/env python3
"""
MCP服务器测试脚本
验证所有MCP服务器是否正确安装和配置
"""

import subprocess
import sys
import os
import json
from pathlib import Path

def test_command(command, description):
    """测试命令是否可以执行"""
    print(f"🧪 测试 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ {description} - 正常")
            return True
        else:
            print(f"❌ {description} - 失败")
            print(f"   错误: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 超时")
        return False
    except Exception as e:
        print(f"❌ {description} - 异常: {e}")
        return False

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    print(f"📁 检查 {description}...")
    if os.path.exists(file_path):
        print(f"✅ {description} - 存在")
        return True
    else:
        print(f"❌ {description} - 不存在")
        return False

def check_config_file():
    """检查配置文件格式"""
    print("📋 检查配置文件格式...")
    config_file = "claude_desktop_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if "mcpServers" not in config:
            print("❌ 配置文件缺少 mcpServers 字段")
            return False
        
        servers = config["mcpServers"]
        print(f"✅ 配置文件格式正确，包含 {len(servers)} 个服务器")
        
        for name, server_config in servers.items():
            print(f"   • {name}: {server_config.get('description', '无描述')}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始MCP服务器安装验证...")
    print("=" * 50)
    
    results = []
    
    # 检查基础环境
    results.append(test_command("node --version", "Node.js"))
    results.append(test_command("python3 --version", "Python3"))
    results.append(test_command("npm --version", "npm"))
    
    print("\n" + "=" * 50)
    
    # 检查虚拟环境
    results.append(check_file_exists("mcp_env", "Python虚拟环境目录"))
    results.append(check_file_exists("mcp_env/bin/python", "虚拟环境Python"))
    
    print("\n" + "=" * 50)
    
    # 检查Node.js MCP服务器
    results.append(test_command("npx @modelcontextprotocol/server-filesystem --help", "Filesystem MCP服务器"))
    results.append(test_command("npx @modelcontextprotocol/server-memory --help", "Memory MCP服务器"))
    results.append(test_command("npx @modelcontextprotocol/server-sequential-thinking --help", "Sequential Thinking MCP服务器"))
    
    print("\n" + "=" * 50)
    
    # 检查Python MCP服务器
    results.append(test_command("./mcp_env/bin/python -m mcp_server_git --help", "Git MCP服务器"))
    results.append(test_command("./mcp_env/bin/python -m mcp_server_time --help", "Time MCP服务器"))
    
    print("\n" + "=" * 50)
    
    # 检查配置文件
    results.append(check_config_file())
    results.append(check_file_exists("README_MCP.md", "README文档"))
    results.append(check_file_exists("setup_mcp.sh", "安装脚本"))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 所有测试通过！MCP服务器安装成功！")
        print("\n📝 下一步:")
        print("1. 重启Claude Desktop")
        print("2. 检查Claude Desktop是否能识别MCP服务器")
        print("3. 开始使用MCP功能")
        return 0
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查安装")
        print("\n🔧 故障排除建议:")
        print("1. 运行 ./setup_mcp.sh 重新安装")
        print("2. 检查Node.js和Python版本")
        print("3. 确保网络连接正常")
        return 1

if __name__ == "__main__":
    sys.exit(main())
