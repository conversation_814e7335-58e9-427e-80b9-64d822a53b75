# MCP服务器安装与配置指南

本项目提供了一套完整的MCP（Model Context Protocol）服务器安装和配置方案，包含多个常用的免费API服务，无需付费即可使用。

## 🚀 快速开始

### 1. 自动安装（推荐）

运行自动安装脚本：

```bash
./setup_mcp.sh
```

### 2. 手动安装

如果需要手动安装，请按以下步骤操作：

#### 安装Node.js MCP服务器

```bash
npm install -g @modelcontextprotocol/server-filesystem
npm install -g @modelcontextprotocol/server-memory
npm install -g @modelcontextprotocol/server-sequential-thinking
```

#### 安装Python MCP服务器

```bash
# 创建虚拟环境
python3 -m venv mcp_env
source mcp_env/bin/activate

# 安装MCP服务器
pip install mcp-server-git
pip install mcp-server-time
```

## 📋 已配置的MCP服务器

### 1. 文件系统服务器 (filesystem)
- **功能**: 文件和目录操作
- **类型**: Node.js官方服务器
- **无需API密钥**: ✅
- **功能列表**:
  - 读取文件内容
  - 写入文件
  - 创建/删除目录
  - 列出目录内容
  - 搜索文件

### 2. 记忆服务器 (memory)
- **功能**: 知识图谱和记忆存储
- **类型**: Node.js官方服务器
- **无需API密钥**: ✅
- **功能列表**:
  - 创建实体和关系
  - 存储观察记录
  - 搜索知识
  - 导出记忆数据

### 3. 顺序思考服务器 (sequential-thinking)
- **功能**: 结构化问题解决
- **类型**: Node.js官方服务器
- **无需API密钥**: ✅
- **功能列表**:
  - 分步骤思考
  - 逻辑推理
  - 问题分解

### 4. Git服务器 (git)
- **功能**: Git版本控制操作
- **类型**: Python服务器
- **无需API密钥**: ✅
- **功能列表**:
  - 查看Git状态
  - 提交更改
  - 查看历史记录
  - 分支操作

### 5. 时间服务器 (time)
- **功能**: 时间和日期处理
- **类型**: Python服务器
- **无需API密钥**: ✅
- **功能列表**:
  - 获取当前时间
  - 时区转换
  - 日期计算
  - 格式化时间

## 🔧 配置文件

### Claude Desktop配置

配置文件位置：`~/.config/claude-desktop/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>"],
      "description": "文件系统操作服务器"
    },
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "description": "知识图谱记忆服务器"
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
      "description": "顺序思考服务器"
    },
    "git": {
      "command": "./mcp_env/bin/python",
      "args": ["-m", "mcp_server_git", "--repository", "/home/<USER>/mcp"],
      "description": "Git版本控制服务器"
    },
    "time": {
      "command": "./mcp_env/bin/python",
      "args": ["-m", "mcp_server_time"],
      "description": "时间服务器"
    }
  }
}
```

## 🧪 测试MCP服务器

### 测试时间服务器

```bash
./mcp_env/bin/python -m mcp_server_time --help
```

### 测试Git服务器

```bash
./mcp_env/bin/python -m mcp_server_git --help
```

## 📚 使用说明

1. **重启Claude Desktop**: 安装完成后，请重启Claude Desktop以加载新的MCP服务器配置
2. **验证安装**: 在Claude Desktop中，你应该能看到新的工具和功能
3. **开始使用**: 现在可以通过自然语言与这些MCP服务器交互

## 🔍 故障排除

### 常见问题

1. **Node.js版本过低**
   - 确保Node.js版本 >= 18
   - 运行 `node --version` 检查版本

2. **Python虚拟环境问题**
   - 确保激活了虚拟环境：`source mcp_env/bin/activate`
   - 检查Python版本：`python --version`

3. **权限问题**
   - 确保脚本有执行权限：`chmod +x setup_mcp.sh`
   - 检查文件路径是否正确

4. **Claude Desktop无法连接MCP服务器**
   - 检查配置文件路径是否正确
   - 确保所有依赖都已安装
   - 重启Claude Desktop

## 🎯 下一步

- 探索更多MCP服务器：访问 [Smithery.ai](https://smithery.ai) 发现更多服务器
- 自定义配置：根据需要修改配置文件
- 开发自己的MCP服务器：参考官方文档

## 📞 支持

如果遇到问题，请检查：
1. 所有依赖是否正确安装
2. 配置文件格式是否正确
3. 路径是否正确设置

祝你使用愉快！🎉
